// --- Provided Data ---
const graphData = {
    nodes: [
        { "id": "<PERSON><PERSON><PERSON>", "x": 0.9134213014976535, "y": 0.2540740323898225 },
        { "id": "<PERSON><PERSON><PERSON><PERSON>", "x": 0.14374226893980302, "y": 0.3910154112946962 },
        { "id": "Chirad<PERSON><PERSON>", "x": 0.9351749046225152, "y": 0.5027042682331085 },
        { "id": "Chi<PERSON><PERSON>", "x": 0.5033532302137712, "y": 0.6371050642113303 },
        { "id": "Dedza", "x": 0.32675593364689126, "y": 0.32741458873737384 },
        { "id": "Dowa", "x": 0.44893854232683894, "y": 0.3534310438093927 },
        { "id": "<PERSON><PERSON><PERSON>", "x": 0.7719114930591756, "y": 0.7164846847486838 },
        { "id": "<PERSON>sun<PERSON>", "x": 0.9486271739760203, "y": 0.03717616769235954 },
        { "id": "Lilongwe", "x": 0.03185092819745572, "y": 0.07907784991666855 },
        { "id": "Machinga", "x": 0.4976553188158377, "y": 0.15957191749775634 },
        { "id": "Mangochi", "x": 0.2417748469656349, "y": 0.22132470346325728 },
        { "id": "Mchinji", "x": 0.8029651384628501, "y": 0.4170419722297135 },
        { "id": "Mulanje", "x": 0.6998851394303303, "y": 0.7300336822154281 },
        { "id": "Mwanza", "x": 0.3093976112949879, "y": 0.9141857772478698 },
        { "id": "Mzimba", "x": 0.16190201617155997, "y": 0.8356366262711726 },
        { "id": "Neno", "x": 0.9869012833729535, "y": 0.3511167097222222 },
        { "id": "Nkhata Bay", "x": 0.0882233026546202, "y": 0.18674223158715342 },
        { "id": "Nkhotakota", "x": 0.17467106409589772, "y": 0.0010883823237957113 },
        { "id": "Nsanje", "x": 0.8093914854184416, "y": 0.5079865816371467 },
        { "id": "Ntcheu", "x": 0.8588177668360885, "y": 0.4167540312634731 },
        { "id": "Ntchisi", "x": 0.3969781197576786, "y": 0.9982702660465445 },
        { "id": "Phalombe", "x": 0.934352810085411, "y": 0.7328019939159007 },
        { "id": "Rumphi", "x": 0.2438492080065875, "y": 0.0387865957339274 },
        { "id": "Salima", "x": 0.837201462046805, "y": 0.9965726289086905 },
        { "id": "Thyolo", "x": 0.6272655175304893, "y": 0.7688215502317457 },
        { "id": "Zomba", "x": 0.7252659639019722, "y": 0.810888016094619 },
        { "id": "Balaka", "x": 0.15932838570160823, "y": 0.5698123530031478 },
        { "id": "Likoma", "x": 0.3488343806746971, "y": 0.6253864059894712 }
    ],
    edges: [
        ["Blantyre", "Chikwawa"], ["Blantyre", "Chiradzulu"], ["Blantyre", "Thyolo"],
        ["Chikwawa", "Nsanje"], ["Chikwawa", "Mwanza"], ["Chiradzulu", "Zomba"],
        ["Chiradzulu", "Phalombe"], ["Chitipa", "Karonga"], ["Dedza", "Lilongwe"],
        ["Dedza", "Ntcheu"], ["Dowa", "Lilongwe"], ["Dowa", "Ntchisi"],
        ["Karonga", "Rumphi"], ["Kasungu", "Lilongwe"], ["Kasungu", "Mzimba"],
        ["Lilongwe", "Mchinji"], ["Lilongwe", "Salima"], ["Machinga", "Zomba"],
        ["Machinga", "Balaka"], ["Mangochi", "Balaka"], ["Mangochi", "Salima"],
        ["Mulanje", "Phalombe"], ["Mulanje", "Thyolo"], ["Mwanza", "Neno"],
        ["Mzimba", "Nkhata Bay"], ["Mzimba", "Rumphi"], ["Nkhata Bay", "Nkhotakota"],
        ["Nkhotakota", "Salima"], ["Nsanje", "Chikwawa"], ["Ntcheu", "Balaka"],
        ["Ntchisi", "Nkhotakota"], ["Phalombe", "Mulanje"], ["Salima", "Nkhotakota"],
        ["Zomba", "Machinga"]
    ]
};

/**
 * Implements a basic Force-Directed Graph Layout algorithm.
 *
 * @param {Array<Object>} nodes - Array of node objects, each with 'id', 'x', 'y'.
 * @param {Array<Array<string>>} edges - Array of edge pairs (sourceId, targetId).
 * @param {number} iterations - Number of simulation iterations.
 * @param {number} repulsionConstant - Strength of repulsive forces between nodes.
 * @param {number} attractionConstant - Strength of attractive forces along edges.
 * @param {number} dampingFactor - Reduces movement over iterations, aiding convergence.
 * @param {number} optimalDistance - The ideal distance between connected nodes.
 * @returns {Array<Object>} - New array of node objects with optimized 'x', 'y' positions.
 */
function forceDirectedLayout(
    nodes,
    edges,
    iterations = 500, // Increased iterations for better stability
    repulsionConstant = 0.05, // Adjusted repulsion
    attractionConstant = 0.01, // Adjusted attraction
    dampingFactor = 0.8, // Slightly stronger damping
    optimalDistance = 0.1 // Ideal edge length (a reference for attraction)
) {
    // Create a mutable copy of nodes, and map node IDs to their objects for quick lookup
    const mutableNodes = nodes.map(node => ({ ...node, fx: 0, fy: 0 })); // fx, fy for force accumulation
    const nodeMap = new Map(mutableNodes.map(node => [node.id, node]));

    for (let i = 0; i < iterations; i++) {
        // Reset forces for the current iteration
        mutableNodes.forEach(node => {
            node.fx = 0;
            node.fy = 0;
        });

        // 1. Calculate Repulsive Forces (between all pairs of nodes)
        for (let j = 0; j < mutableNodes.length; j++) {
            const node1 = mutableNodes[j];
            for (let k = j + 1; k < mutableNodes.length; k++) {
                const node2 = mutableNodes[k];

                const dx = node2.x - node1.x;
                const dy = node2.y - node1.y;
                let distance = Math.sqrt(dx * dx + dy * dy);

                // Prevent division by zero if nodes are exactly on top of each other
                if (distance === 0) {
                    distance = 0.001; // Small epsilon to avoid infinite force
                }

                // Coulomb's law-like repulsion: F = k_rep / d^2
                const force = repulsionConstant / (distance * distance);

                // Apply force in opposite directions along the vector connecting them
                const angle = Math.atan2(dy, dx);
                node1.fx -= force * Math.cos(angle);
                node1.fy -= force * Math.sin(angle);
                node2.fx += force * Math.cos(angle);
                node2.fy += force * Math.sin(angle);
            }
        }

        // 2. Calculate Attractive Forces (along edges)
        for (const edge of edges) {
            const sourceNode = nodeMap.get(edge[0]);
            const targetNode = nodeMap.get(edge[1]);

            if (!sourceNode || !targetNode) {
                console.warn(`Edge refers to non-existent node: ${edge[0]} or ${edge[1]}`);
                continue;
            }

            const dx = targetNode.x - sourceNode.x;
            const dy = targetNode.y - sourceNode.y;
            let distance = Math.sqrt(dx * dx + dy * dy);

            if (distance === 0) {
                distance = 0.001; // Small epsilon to avoid infinite force
            }

            // Hooke's law-like attraction: F = k_attr * (d - optimalDistance)
            const force = attractionConstant * (distance - optimalDistance);

            // Apply force along the vector connecting them
            const angle = Math.atan2(dy, dx);
            sourceNode.fx += force * Math.cos(angle);
            sourceNode.fy += force * Math.sin(angle);
            targetNode.fx -= force * Math.cos(angle);
            targetNode.fy -= force * Math.sin(angle);
        }

        // 3. Update Node Positions and Apply Boundary Constraints
        for (const node of mutableNodes) {
            // Apply damping to reduce oscillations and aid convergence
            node.x += node.fx * dampingFactor;
            node.y += node.fy * dampingFactor;

            // Enforce 1x1 unit square boundary
            node.x = Math.max(0, Math.min(1, node.x));
            node.y = Math.max(0, Math.min(1, node.y));
        }
    }

    // Return only the ID and new positions
    return mutableNodes.map(node => ({ id: node.id, x: node.x, y: node.y }));
}

// --- Apply the algorithm ---
const optimizedPositions = forceDirectedLayout(graphData.nodes, graphData.edges);

// --- Output the new positions ---
console.log("Optimized Positions for Malawi Districts:");
optimizedPositions.forEach(node => {
    console.log(`Node ${node.id}: (X: ${node.x.toFixed(6)}, Y: ${node.y.toFixed(6)})`);
});

/*
// --- Optional: Visualization Code (for browser environment) ---
// To run this part, you'd typically have an HTML file with a <canvas> element.

// Uncomment and copy-paste into an HTML <script> tag if visualizing in browser:

// <canvas id="graphCanvas" width="800" height="800" style="border:1px solid #ccc; background-color: #f9f9f9;"></canvas>
// <script>
//     const canvas = document.getElementById('graphCanvas');
//     const ctx = canvas.getContext('2d');
//     const canvasSize = 800; // Size of our canvas in pixels
//     const padding = 50; // Padding from canvas edges
//     const effectiveSize = canvasSize - 2 * padding; // Actual area for 1x1 unit square

//     function drawGraph(nodes, edges) {
//         ctx.clearRect(0, 0, canvasSize, canvasSize); // Clear canvas

//         // Translate and scale to fit 1x1 unit square into the padded canvas area
//         ctx.save();
//         ctx.translate(padding, padding);
//         ctx.scale(effectiveSize, effectiveSize);

//         // Draw edges
//         ctx.strokeStyle = '#999';
//         ctx.lineWidth = 0.002; // Scaled line width
//         edges.forEach(edge => {
//             const source = nodes.find(n => n.id === edge[0]);
//             const target = nodes.find(n => n.id === edge[1]);
//             if (source && target) {
//                 ctx.beginPath();
//                 ctx.moveTo(source.x, source.y);
//                 ctx.lineTo(target.x, target.y);
//                 ctx.stroke();
//             }
//         });

//         // Draw nodes
//         ctx.fillStyle = 'steelblue';
//         ctx.strokeStyle = '#fff';
//         ctx.lineWidth = 0.001; // Scaled line width
//         nodes.forEach(node => {
//             ctx.beginPath();
//             ctx.arc(node.x, node.y, 0.01, 0, 2 * Math.PI); // Node radius, scaled
//             ctx.fill();
//             ctx.stroke();

//             // Draw node labels (scaled down)
//             ctx.save();
//             ctx.translate(node.x, node.y);
//             ctx.scale(1/effectiveSize, 1/effectiveSize); // Scale text back to readable size
//             ctx.fillStyle = 'black';
//             ctx.font = '12px Arial';
//             ctx.fillText(node.id, 15, 5); // Offset text from node center
//             ctx.restore();
//         });

//         ctx.restore(); // Restore canvas context to original state
//     }

//     // Call drawGraph with the optimized positions
//     drawGraph(optimizedPositions, graphData.edges);
// </script>
*/