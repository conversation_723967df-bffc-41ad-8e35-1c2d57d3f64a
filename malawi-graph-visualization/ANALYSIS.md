# Malawi Districts Graph Visualization - Problem Analysis

## Problem Analysis

### Graph Visualization Challenges

The Malawi districts graph presents several key visualization challenges:

1. **Node Overlap**: With 28 districts randomly positioned in a 1x1 space, many nodes overlap, making individual districts impossible to identify.

2. **Edge Crossings**: Random positioning creates numerous unnecessary edge crossings, significantly reducing the readability of the network structure.

3. **Spatial Distribution**: The initial layout doesn't reflect the logical relationships between connected districts, making it difficult to understand the network topology.

4. **Visual Clutter**: Poor positioning creates visual noise that obscures the underlying structure of district connections.

5. **Constraint Satisfaction**: All optimized positions must remain within the [0,1] unit square while improving the layout quality.

### Graph Structure Analysis

**Network Properties:**
- **Nodes**: 28 districts
- **Edges**: 34 connections  
- **Density**: 8.7% (sparse network)
- **Average Degree**: 2.43 connections per district
- **Degree Range**: 1-5 connections

**Key Observations:**
- The graph is relatively sparse, suggesting districts have selective connections
- Some districts (like Lilongwe) serve as hubs with multiple connections
- The network has a tree-like structure with some cycles
- Geographic proximity likely influences connections

## Algorithm Selection: Force-Directed Layout

### Why Force-Directed Layout?

I selected the **Fruchterman-Reingold force-directed algorithm** for the following reasons:

1. **Spring Forces**: Models connected nodes as springs, naturally pulling related districts closer together
2. **Repulsion Forces**: Prevents node overlap by making all nodes repel each other
3. **Iterative Optimization**: Gradually improves the layout through multiple iterations
4. **Constraint Handling**: Easy to implement boundary constraints
5. **Proven Effectiveness**: Well-established algorithm for network visualization

### Algorithm Mechanics

**Force Calculation:**
- **Attraction Force**: F_spring = c1 * log(distance / k)
- **Repulsion Force**: F_repulsion = c2 * k² / distance
- **Net Force**: Vector sum of all forces acting on each node

**Position Updates:**
- Velocity = (Velocity + Force) * damping
- Position = Position + Velocity
- Boundary constraints applied after each update

## Approach Explanation

### Parameter Optimization

Based on the Malawi graph structure, I calculated optimal parameters:

1. **Optimal Distance (k)**: 0.15
   - Calculated as √(1/28) * 1.2 ≈ 0.15
   - Represents ideal spacing for 28 nodes in 1x1 space

2. **Spring Force (c1)**: 2.5
   - Higher value for sparse graphs to ensure connected districts stay close
   - Adjusted based on network density (8.7%)

3. **Repulsion Force (c2)**: 1.2
   - Balanced to prevent overlap without over-spreading
   - Scaled with average degree (2.43)

4. **Damping**: 0.85
   - High damping for stable convergence
   - Prevents oscillation in final iterations

5. **Iterations**: 600
   - Sufficient for convergence with 28 nodes and 34 edges
   - Formula: max(300, nodes * 15 + edges * 5)

### Implementation Strategy

1. **Data Processing**: Parse adjacency list and initial positions
2. **Force Calculation**: Compute spring and repulsion forces for each node
3. **Position Updates**: Apply forces with velocity damping
4. **Constraint Enforcement**: Keep all nodes within [0,1] bounds
5. **Iteration**: Repeat until convergence or maximum iterations
6. **Visualization**: Render optimized layout with clear node and edge representation

### Quality Metrics

The algorithm optimizes for:
- **Minimal Edge Crossings**: Reduces visual complexity
- **Balanced Distribution**: Even spacing across the canvas
- **Preserved Connectivity**: Connected districts remain reasonably close
- **Boundary Compliance**: All positions stay within valid range

## Expected Outcomes

### Quantitative Improvements
- **Edge Crossings**: Reduced by 60-80% from initial layout
- **Node Overlap**: Eliminated through repulsion forces
- **Distribution Variance**: More uniform spacing across the 1x1 space
- **Average Edge Length**: Optimized for readability

### Qualitative Benefits
- **Visual Clarity**: Clear identification of individual districts
- **Network Structure**: Obvious clustering of connected regions
- **Relationship Visibility**: Easy to trace connections between districts
- **Professional Appearance**: Clean, organized layout suitable for presentation

## Technical Considerations

### Performance
- **Time Complexity**: O(n² * iterations) for force calculations
- **Space Complexity**: O(n) for storing positions and velocities
- **Execution Time**: ~50-200ms for 28 nodes on modern browsers

### Scalability
- Algorithm scales well up to ~100 nodes
- Parameter adjustment needed for different graph sizes
- Memory usage remains minimal for typical network sizes

### Browser Compatibility
- Pure JavaScript implementation (ES6+)
- Canvas API for rendering
- No external dependencies required
- Works on all modern browsers

This analysis demonstrates a systematic approach to graph visualization, combining theoretical understanding with practical implementation to solve the Malawi districts layout optimization problem.
