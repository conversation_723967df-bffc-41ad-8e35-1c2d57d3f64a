// Graph Analysis Script for Optimal Parameter Selection

function analyzeGraph(nodes, edges) {
    const analysis = {
        nodeCount: nodes.length,
        edgeCount: edges.length,
        density: 0,
        avgDegree: 0,
        maxDegree: 0,
        minDegree: Infinity,
        avgDistance: 0,
        diameter: 0,
        clustering: 0
    };
    
    // Calculate graph density
    const maxPossibleEdges = (nodes.length * (nodes.length - 1)) / 2;
    analysis.density = edges.length / maxPossibleEdges;
    
    // Calculate degree distribution
    const degrees = new Map();
    nodes.forEach(node => degrees.set(node.id, 0));
    
    edges.forEach(edge => {
        degrees.set(edge[0], degrees.get(edge[0]) + 1);
        degrees.set(edge[1], degrees.get(edge[1]) + 1);
    });
    
    const degreeValues = Array.from(degrees.values());
    analysis.avgDegree = degreeValues.reduce((sum, d) => sum + d, 0) / degreeValues.length;
    analysis.maxDegree = Math.max(...degreeValues);
    analysis.minDegree = Math.min(...degreeValues);
    
    // Calculate average distance between nodes
    let totalDistance = 0;
    let pairCount = 0;
    
    for (let i = 0; i < nodes.length; i++) {
        for (let j = i + 1; j < nodes.length; j++) {
            const dx = nodes[i].x - nodes[j].x;
            const dy = nodes[i].y - nodes[j].y;
            totalDistance += Math.sqrt(dx * dx + dy * dy);
            pairCount++;
        }
    }
    
    analysis.avgDistance = totalDistance / pairCount;
    
    return analysis;
}

function calculateOptimalParameters(analysis) {
    const { nodeCount, edgeCount, density, avgDegree, avgDistance } = analysis;
    
    // Optimal distance between nodes (k parameter)
    // For 28 nodes in 1x1 space, we want nodes to be roughly sqrt(1/28) apart
    const optimalK = Math.sqrt(1.0 / nodeCount) * 1.2; // ~0.2
    
    // Spring force constant (c1)
    // Higher for denser graphs to pull connected nodes together
    // Lower for sparse graphs to avoid over-clustering
    const springForce = Math.max(1.0, Math.min(4.0, 2.0 + density * 2.0));
    
    // Repulsion force constant (c2)
    // Higher for dense graphs to spread nodes out
    // Adjusted based on average degree
    const repulsionForce = Math.max(0.5, Math.min(2.5, 1.0 + avgDegree * 0.1));
    
    // Damping factor
    // Higher damping for more stable convergence
    const damping = 0.85;
    
    // Iterations
    // More iterations for larger, denser graphs
    const iterations = Math.max(300, Math.min(1000, nodeCount * 15 + edgeCount * 5));
    
    return {
        k: optimalK,
        c1: springForce,
        c2: repulsionForce,
        damping: damping,
        iterations: iterations
    };
}

// Analyze the Malawi graph
console.log('=== Malawi Districts Graph Analysis ===');

// Load data (assuming malawiData is available)
if (typeof malawiData !== 'undefined') {
    const analysis = analyzeGraph(malawiData.nodes, malawiData.edges);
    
    console.log('\nGraph Statistics:');
    console.log(`Nodes: ${analysis.nodeCount}`);
    console.log(`Edges: ${analysis.edgeCount}`);
    console.log(`Density: ${(analysis.density * 100).toFixed(2)}%`);
    console.log(`Average Degree: ${analysis.avgDegree.toFixed(2)}`);
    console.log(`Degree Range: ${analysis.minDegree} - ${analysis.maxDegree}`);
    console.log(`Average Distance: ${analysis.avgDistance.toFixed(4)}`);
    
    const optimal = calculateOptimalParameters(analysis);
    
    console.log('\nOptimal Parameters:');
    console.log(`k (optimal distance): ${optimal.k.toFixed(3)}`);
    console.log(`c1 (spring force): ${optimal.c1.toFixed(1)}`);
    console.log(`c2 (repulsion force): ${optimal.c2.toFixed(1)}`);
    console.log(`damping: ${optimal.damping.toFixed(2)}`);
    console.log(`iterations: ${optimal.iterations}`);
    
    // Degree distribution analysis
    console.log('\nDegree Distribution:');
    const degrees = new Map();
    malawiData.nodes.forEach(node => degrees.set(node.id, 0));
    malawiData.edges.forEach(edge => {
        degrees.set(edge[0], degrees.get(edge[0]) + 1);
        degrees.set(edge[1], degrees.get(edge[1]) + 1);
    });
    
    const sortedDegrees = Array.from(degrees.entries()).sort((a, b) => b[1] - a[1]);
    console.log('Top connected districts:');
    sortedDegrees.slice(0, 5).forEach(([district, degree]) => {
        console.log(`  ${district}: ${degree} connections`);
    });
}
