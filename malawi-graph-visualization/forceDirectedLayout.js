/**
 * Force-Directed Graph Layout Algorithm
 * Implementation of Fr<PERSON><PERSON>man-<PERSON> algorithm for graph visualization
 */

class ForceDirectedLayout {
  constructor(options = {}) {
    // Algorithm parameters
    this.width = options.width || 1.0;
    this.height = options.height || 1.0;
    this.iterations = options.iterations || 600;
    this.k = options.k || 0.15; // Optimal distance between nodes (calculated for 28 nodes)
    this.c1 = options.c1 || 2.5; // Spring force constant
    this.c2 = options.c2 || 1.2; // Repulsion force constant
    this.damping = options.damping || 0.85; // Velocity damping
    this.minDistance = options.minDistance || 0.01; // Minimum distance to prevent division by zero

    // State
    this.nodes = [];
    this.edges = [];
    this.velocities = new Map();
  }

  /**
   * Initialize the layout with graph data
   */
  setGraph(nodes, edges) {
    this.nodes = nodes.map((node) => ({
      id: node.id,
      x: node.x,
      y: node.y,
    }));

    this.edges = edges;

    // Initialize velocities
    this.velocities.clear();
    this.nodes.forEach((node) => {
      this.velocities.set(node.id, { vx: 0, vy: 0 });
    });
  }

  /**
   * Calculate repulsion force between two nodes
   */
  calculateRepulsionForce(node1, node2) {
    const dx = node1.x - node2.x;
    const dy = node1.y - node2.y;
    const distance = Math.max(Math.sqrt(dx * dx + dy * dy), this.minDistance);

    const force = (this.c2 * this.k * this.k) / distance;
    const fx = (force * dx) / distance;
    const fy = (force * dy) / distance;

    return { fx, fy };
  }

  /**
   * Calculate spring force between connected nodes
   */
  calculateSpringForce(node1, node2) {
    const dx = node2.x - node1.x;
    const dy = node2.y - node1.y;
    const distance = Math.max(Math.sqrt(dx * dx + dy * dy), this.minDistance);

    const force = this.c1 * Math.log(distance / this.k);
    const fx = (force * dx) / distance;
    const fy = (force * dy) / distance;

    return { fx, fy };
  }

  /**
   * Apply boundary constraints to keep nodes within [0,1] bounds
   */
  applyBoundaryConstraints(node) {
    const margin = 0.05; // Small margin from edges
    node.x = Math.max(margin, Math.min(this.width - margin, node.x));
    node.y = Math.max(margin, Math.min(this.height - margin, node.y));
  }

  /**
   * Perform one iteration of the force-directed algorithm
   */
  iterate() {
    const forces = new Map();

    // Initialize forces
    this.nodes.forEach((node) => {
      forces.set(node.id, { fx: 0, fy: 0 });
    });

    // Calculate repulsion forces between all pairs of nodes
    for (let i = 0; i < this.nodes.length; i++) {
      for (let j = i + 1; j < this.nodes.length; j++) {
        const node1 = this.nodes[i];
        const node2 = this.nodes[j];

        const repulsion = this.calculateRepulsionForce(node1, node2);

        // Apply force to node1
        const force1 = forces.get(node1.id);
        force1.fx += repulsion.fx;
        force1.fy += repulsion.fy;

        // Apply opposite force to node2
        const force2 = forces.get(node2.id);
        force2.fx -= repulsion.fx;
        force2.fy -= repulsion.fy;
      }
    }

    // Calculate spring forces for connected nodes
    this.edges.forEach((edge) => {
      const node1 = this.nodes.find((n) => n.id === edge[0]);
      const node2 = this.nodes.find((n) => n.id === edge[1]);

      if (node1 && node2) {
        const spring = this.calculateSpringForce(node1, node2);

        // Apply spring force
        const force1 = forces.get(node1.id);
        force1.fx += spring.fx;
        force1.fy += spring.fy;

        const force2 = forces.get(node2.id);
        force2.fx -= spring.fx;
        force2.fy -= spring.fy;
      }
    });

    // Update velocities and positions
    this.nodes.forEach((node) => {
      const force = forces.get(node.id);
      const velocity = this.velocities.get(node.id);

      // Update velocity with damping
      velocity.vx = (velocity.vx + force.fx) * this.damping;
      velocity.vy = (velocity.vy + force.fy) * this.damping;

      // Update position
      node.x += velocity.vx;
      node.y += velocity.vy;

      // Apply boundary constraints
      this.applyBoundaryConstraints(node);
    });
  }

  /**
   * Run the complete layout algorithm
   */
  layout() {
    console.log(
      `Starting force-directed layout with ${this.iterations} iterations...`
    );

    for (let i = 0; i < this.iterations; i++) {
      this.iterate();

      // Log progress every 100 iterations
      if ((i + 1) % 100 === 0) {
        console.log(`Iteration ${i + 1}/${this.iterations} completed`);
      }
    }

    console.log("Layout optimization completed!");
    return this.nodes;
  }

  /**
   * Get the current node positions
   */
  getPositions() {
    return this.nodes.map((node) => ({
      id: node.id,
      x: parseFloat(node.x.toFixed(6)),
      y: parseFloat(node.y.toFixed(6)),
    }));
  }
}
