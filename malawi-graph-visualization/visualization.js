/**
 * Graph Visualization Component
 * Handles canvas rendering and user interactions
 */

class GraphVisualization {
  constructor(canvasId, width = 400, height = 400) {
    this.canvas = document.getElementById(canvasId);
    this.ctx = this.canvas.getContext("2d");
    this.width = width;
    this.height = height;
    this.nodes = [];
    this.edges = [];
    this.nodeRadius = 8;
    this.nodeColors = {
      default: "#4CAF50",
      hover: "#45a049",
      text: "#333",
    };
    this.edgeColor = "#999";
    this.edgeWidth = 1;
  }

  /**
   * Set graph data for visualization
   */
  setGraph(nodes, edges) {
    this.nodes = nodes;
    this.edges = edges;
  }

  /**
   * Convert normalized coordinates (0-1) to canvas coordinates
   */
  normalizedToCanvas(x, y) {
    const margin = 30;
    const canvasX = margin + x * (this.width - 2 * margin);
    const canvasY = margin + y * (this.height - 2 * margin);
    return { x: canvasX, y: canvasY };
  }

  /**
   * Draw an edge between two nodes
   */
  drawEdge(node1, node2) {
    const pos1 = this.normalizedToCanvas(node1.x, node1.y);
    const pos2 = this.normalizedToCanvas(node2.x, node2.y);

    this.ctx.strokeStyle = this.edgeColor;
    this.ctx.lineWidth = this.edgeWidth;
    this.ctx.beginPath();
    this.ctx.moveTo(pos1.x, pos1.y);
    this.ctx.lineTo(pos2.x, pos2.y);
    this.ctx.stroke();
  }

  /**
   * Draw a node (district)
   */
  drawNode(node) {
    const pos = this.normalizedToCanvas(node.x, node.y);

    // Draw node circle
    this.ctx.fillStyle = this.nodeColors.default;
    this.ctx.beginPath();
    this.ctx.arc(pos.x, pos.y, this.nodeRadius, 0, 2 * Math.PI);
    this.ctx.fill();

    // Draw node border
    this.ctx.strokeStyle = "#333";
    this.ctx.lineWidth = 1;
    this.ctx.stroke();

    // Draw node label
    this.ctx.fillStyle = this.nodeColors.text;
    this.ctx.font = "10px Arial";
    this.ctx.textAlign = "center";
    this.ctx.textBaseline = "middle";

    // Position label slightly below the node
    this.ctx.fillText(node.id, pos.x, pos.y + this.nodeRadius + 12);
  }

  /**
   * Clear the canvas
   */
  clear() {
    this.ctx.clearRect(0, 0, this.width, this.height);

    // Draw background
    this.ctx.fillStyle = "#fafafa";
    this.ctx.fillRect(0, 0, this.width, this.height);
  }

  /**
   * Render the complete graph
   */
  render() {
    this.clear();

    // Draw edges first (so they appear behind nodes)
    this.edges.forEach((edge) => {
      const node1 = this.nodes.find((n) => n.id === edge[0]);
      const node2 = this.nodes.find((n) => n.id === edge[1]);
      if (node1 && node2) {
        this.drawEdge(node1, node2);
      }
    });

    // Draw nodes on top
    this.nodes.forEach((node) => {
      this.drawNode(node);
    });
  }

  /**
   * Calculate and display graph statistics
   */
  calculateStats() {
    const edgeCrossings = this.countEdgeCrossings();
    const avgEdgeLength = this.calculateAverageEdgeLength();
    const nodeSpread = this.calculateNodeSpread();

    return {
      edgeCrossings,
      avgEdgeLength,
      nodeSpread,
    };
  }

  /**
   * Count edge crossings (simplified algorithm)
   */
  countEdgeCrossings() {
    let crossings = 0;
    const edgeLines = [];

    // Convert edges to line segments
    this.edges.forEach((edge) => {
      const node1 = this.nodes.find((n) => n.id === edge[0]);
      const node2 = this.nodes.find((n) => n.id === edge[1]);
      if (node1 && node2) {
        edgeLines.push({
          x1: node1.x,
          y1: node1.y,
          x2: node2.x,
          y2: node2.y,
        });
      }
    });

    // Check for intersections
    for (let i = 0; i < edgeLines.length; i++) {
      for (let j = i + 1; j < edgeLines.length; j++) {
        if (this.linesIntersect(edgeLines[i], edgeLines[j])) {
          crossings++;
        }
      }
    }

    return crossings;
  }

  /**
   * Check if two line segments intersect
   */
  linesIntersect(line1, line2) {
    const { x1, y1, x2, y2 } = line1;
    const { x1: x3, y1: y3, x2: x4, y2: y4 } = line2;

    const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
    if (Math.abs(denom) < 1e-10) return false; // Parallel lines

    const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom;
    const u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom;

    return t >= 0 && t <= 1 && u >= 0 && u <= 1;
  }

  /**
   * Calculate average edge length
   */
  calculateAverageEdgeLength() {
    let totalLength = 0;
    let edgeCount = 0;

    this.edges.forEach((edge) => {
      const node1 = this.nodes.find((n) => n.id === edge[0]);
      const node2 = this.nodes.find((n) => n.id === edge[1]);
      if (node1 && node2) {
        const dx = node1.x - node2.x;
        const dy = node1.y - node2.y;
        totalLength += Math.sqrt(dx * dx + dy * dy);
        edgeCount++;
      }
    });

    return edgeCount > 0 ? totalLength / edgeCount : 0;
  }

  /**
   * Calculate node spread (standard deviation of positions)
   */
  calculateNodeSpread() {
    if (this.nodes.length === 0) return 0;

    const avgX =
      this.nodes.reduce((sum, node) => sum + node.x, 0) / this.nodes.length;
    const avgY =
      this.nodes.reduce((sum, node) => sum + node.y, 0) / this.nodes.length;

    const variance =
      this.nodes.reduce((sum, node) => {
        const dx = node.x - avgX;
        const dy = node.y - avgY;
        return sum + dx * dx + dy * dy;
      }, 0) / this.nodes.length;

    return Math.sqrt(variance);
  }
}

/**
 * Main Application Controller
 */
class MalawiGraphApp {
  constructor() {
    this.originalViz = new GraphVisualization("originalCanvas");
    this.optimizedViz = new GraphVisualization("optimizedCanvas");
    this.layout = new ForceDirectedLayout();
    this.originalNodes = [];
    this.optimizedNodes = [];

    this.initializeApp();
    this.setupEventListeners();
  }

  /**
   * Initialize the application with data
   */
  initializeApp() {
    // Store original positions
    this.originalNodes = JSON.parse(JSON.stringify(malawiData.nodes));
    this.optimizedNodes = JSON.parse(JSON.stringify(malawiData.nodes));

    // Set up visualizations
    this.originalViz.setGraph(this.originalNodes, malawiData.edges);
    this.optimizedViz.setGraph(this.optimizedNodes, malawiData.edges);

    // Initial render
    this.originalViz.render();
    this.optimizedViz.render();

    // Update stats
    this.updateStats();
  }

  /**
   * Setup event listeners for controls
   */
  setupEventListeners() {
    document.getElementById("runLayout").addEventListener("click", () => {
      this.runLayoutAlgorithm();
    });

    document.getElementById("resetPositions").addEventListener("click", () => {
      this.resetToOriginal();
    });
  }

  /**
   * Run the force-directed layout algorithm
   */
  async runLayoutAlgorithm() {
    const button = document.getElementById("runLayout");
    button.disabled = true;
    button.textContent = "Running...";

    try {
      // Get parameters from UI
      const iterations = parseInt(document.getElementById("iterations").value);
      const springForce = parseFloat(
        document.getElementById("springForce").value
      );
      const repulsionForce = parseFloat(
        document.getElementById("repulsionForce").value
      );
      const damping = parseFloat(document.getElementById("damping").value);

      // Configure layout algorithm
      this.layout = new ForceDirectedLayout({
        iterations: iterations,
        c1: springForce,
        c2: repulsionForce,
        damping: damping,
      });

      // Reset to original positions
      this.optimizedNodes = JSON.parse(JSON.stringify(this.originalNodes));
      this.layout.setGraph(this.optimizedNodes, malawiData.edges);

      // Run layout with timing
      const startTime = performance.now();
      const optimizedPositions = this.layout.layout();
      const endTime = performance.now();

      // Update optimized nodes
      this.optimizedNodes = optimizedPositions;
      this.optimizedViz.setGraph(this.optimizedNodes, malawiData.edges);
      this.optimizedViz.render();

      // Update UI
      this.updateStats();
      this.displayOptimizedPositions();
      document.getElementById("iterationCount").textContent = iterations;
      document.getElementById("executionTime").textContent = `${Math.round(
        endTime - startTime
      )}ms`;
    } catch (error) {
      console.error("Error running layout algorithm:", error);
      alert(
        "Error running layout algorithm. Please check the console for details."
      );
    } finally {
      button.disabled = false;
      button.textContent = "Run Layout Algorithm";
    }
  }

  /**
   * Reset to original positions
   */
  resetToOriginal() {
    this.optimizedNodes = JSON.parse(JSON.stringify(this.originalNodes));
    this.optimizedViz.setGraph(this.optimizedNodes, malawiData.edges);
    this.optimizedViz.render();
    this.updateStats();
    document.getElementById("positionsOutput").textContent =
      'Click "Run Layout Algorithm" to see optimized positions...';
    document.getElementById("iterationCount").textContent = "0";
    document.getElementById("executionTime").textContent = "0ms";
  }

  /**
   * Update statistics display
   */
  updateStats() {
    document.getElementById("nodeCount").textContent = malawiData.nodes.length;
    document.getElementById("edgeCount").textContent = malawiData.edges.length;
  }

  /**
   * Display optimized positions in JSON format
   */
  displayOptimizedPositions() {
    const output = {
      nodes: this.optimizedNodes.map((node) => ({
        id: node.id,
        x: parseFloat(node.x.toFixed(6)),
        y: parseFloat(node.y.toFixed(6)),
      })),
    };

    document.getElementById("positionsOutput").textContent = JSON.stringify(
      output,
      null,
      2
    );
  }
}

// Initialize the application when the page loads
document.addEventListener("DOMContentLoaded", () => {
  new MalawiGraphApp();
});
