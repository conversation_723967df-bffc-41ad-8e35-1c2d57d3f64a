<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Malawi Districts Graph Visualization</title>
    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        text-align: center;
      }

      .header h1 {
        margin: 0;
        font-size: 2em;
      }

      .header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
      }

      .content {
        padding: 20px;
      }

      .controls {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
        flex-wrap: wrap;
        align-items: center;
      }

      .control-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
      }

      .control-group label {
        font-size: 0.9em;
        font-weight: 600;
        color: #555;
      }

      .control-group input,
      .control-group button {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 0.9em;
      }

      .control-group button {
        background: #667eea;
        color: white;
        border: none;
        cursor: pointer;
        transition: background 0.3s;
      }

      .control-group button:hover {
        background: #5a6fd8;
      }

      .control-group button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }

      .canvas-container {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
      }

      .canvas-wrapper {
        flex: 1;
        text-align: center;
      }

      .canvas-wrapper h3 {
        margin: 0 0 10px 0;
        color: #333;
      }

      canvas {
        border: 2px solid #ddd;
        border-radius: 8px;
        background: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .output {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
      }

      .output h3 {
        margin: 0 0 10px 0;
        color: #333;
      }

      .positions-output {
        background: white;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        max-height: 300px;
        overflow-y: auto;
        font-family: "Courier New", monospace;
        font-size: 0.85em;
        white-space: pre-wrap;
      }

      .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 15px;
      }

      .stat-card {
        background: white;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        text-align: center;
      }

      .stat-value {
        font-size: 1.5em;
        font-weight: bold;
        color: #667eea;
      }

      .stat-label {
        font-size: 0.9em;
        color: #666;
        margin-top: 5px;
      }

      @media (max-width: 768px) {
        .canvas-container {
          flex-direction: column;
        }

        .controls {
          justify-content: center;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Malawi Districts Graph Visualization</h1>
        <p>Force-Directed Layout Algorithm Implementation</p>
      </div>

      <div class="content">
        <div class="controls">
          <div class="control-group">
            <label for="iterations">Iterations:</label>
            <input
              type="number"
              id="iterations"
              value="600"
              min="50"
              max="2000"
              step="50"
            />
          </div>

          <div class="control-group">
            <label for="springForce">Spring Force:</label>
            <input
              type="number"
              id="springForce"
              value="2.5"
              min="0.1"
              max="5.0"
              step="0.1"
            />
          </div>

          <div class="control-group">
            <label for="repulsionForce">Repulsion Force:</label>
            <input
              type="number"
              id="repulsionForce"
              value="1.2"
              min="0.1"
              max="3.0"
              step="0.1"
            />
          </div>

          <div class="control-group">
            <label for="damping">Damping:</label>
            <input
              type="number"
              id="damping"
              value="0.85"
              min="0.1"
              max="1.0"
              step="0.05"
            />
          </div>

          <div class="control-group">
            <label>&nbsp;</label>
            <button id="runLayout">Run Layout Algorithm</button>
          </div>

          <div class="control-group">
            <label>&nbsp;</label>
            <button id="resetPositions">Reset to Original</button>
          </div>
        </div>

        <div class="canvas-container">
          <div class="canvas-wrapper">
            <h3>Original Layout</h3>
            <canvas id="originalCanvas" width="400" height="400"></canvas>
          </div>

          <div class="canvas-wrapper">
            <h3>Optimized Layout</h3>
            <canvas id="optimizedCanvas" width="400" height="400"></canvas>
          </div>
        </div>

        <div class="output">
          <h3>Algorithm Output</h3>
          <div class="stats">
            <div class="stat-card">
              <div class="stat-value" id="nodeCount">28</div>
              <div class="stat-label">Total Districts</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="edgeCount">34</div>
              <div class="stat-label">Connections</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="iterationCount">0</div>
              <div class="stat-label">Iterations Run</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="executionTime">0ms</div>
              <div class="stat-label">Execution Time</div>
            </div>
          </div>

          <h4>Optimized Node Positions (JSON):</h4>
          <div class="positions-output" id="positionsOutput">
            Click "Run Layout Algorithm" to see optimized positions...
          </div>
        </div>
      </div>
    </div>

    <script src="data.js"></script>
    <script src="forceDirectedLayout.js"></script>
    <script src="visualization.js"></script>
  </body>
</html>
