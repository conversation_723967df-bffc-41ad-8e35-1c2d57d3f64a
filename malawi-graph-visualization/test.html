<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Malawi Graph Layout</title>
</head>
<body>
    <h1>Testing Malawi Graph Layout Algorithm</h1>
    <div id="output"></div>

    <script src="data.js"></script>
    <script src="forceDirectedLayout.js"></script>
    <script>
        // Test the algorithm with optimal parameters
        console.log('Testing Malawi Districts Graph Layout...');
        
        const layout = new ForceDirectedLayout({
            iterations: 600,
            c1: 2.5,
            c2: 1.2,
            damping: 0.85,
            k: 0.15
        });
        
        // Set graph data
        layout.setGraph(malawiData.nodes, malawiData.edges);
        
        console.log('Initial positions:');
        console.log(JSON.stringify(layout.getPositions().slice(0, 3), null, 2));
        
        // Run layout
        const startTime = performance.now();
        const optimizedNodes = layout.layout();
        const endTime = performance.now();
        
        console.log(`\nLayout completed in ${Math.round(endTime - startTime)}ms`);
        console.log('\nOptimized positions (first 3 nodes):');
        console.log(JSON.stringify(optimizedNodes.slice(0, 3), null, 2));
        
        console.log('\nAll optimized positions:');
        const allPositions = {
            nodes: optimizedNodes.map(node => ({
                id: node.id,
                x: parseFloat(node.x.toFixed(6)),
                y: parseFloat(node.y.toFixed(6))
            }))
        };
        console.log(JSON.stringify(allPositions, null, 2));
        
        // Display in HTML
        document.getElementById('output').innerHTML = `
            <h2>Algorithm Results</h2>
            <p><strong>Execution Time:</strong> ${Math.round(endTime - startTime)}ms</p>
            <p><strong>Nodes Processed:</strong> ${optimizedNodes.length}</p>
            <p><strong>Edges Processed:</strong> ${malawiData.edges.length}</p>
            <h3>Optimized Positions (JSON):</h3>
            <pre>${JSON.stringify(allPositions, null, 2)}</pre>
        `;
    </script>
</body>
</html>
