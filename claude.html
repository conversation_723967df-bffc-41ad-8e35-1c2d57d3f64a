<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Malawi Districts Graph Visualization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2em;
        }
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .btn-primary {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
        }
        .btn-secondary {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
        }
        .btn-success {
            background: linear-gradient(45deg, #27ae60, #229954);
            color: white;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .canvas-container {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        .canvas-wrapper {
            flex: 1;
            min-width: 300px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .canvas-wrapper h3 {
            color: #2c3e50;
            margin-top: 0;
            text-align: center;
            font-size: 1.3em;
        }
        canvas {
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            display: block;
            margin: 0 auto;
            background: white;
            cursor: crosshair;
        }
        .info-panel {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .metric-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .metric-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
        .metric-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        .coordinates-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .algorithm-info {
            background: #f1c40f;
            color: #2c3e50;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .algorithm-info h4 {
            margin-top: 0;
            color: #2c3e50;
        }
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #3498db, #2980b9);
            transition: width 0.3s ease;
            width: 0%;
        }
        @media (max-width: 768px) {
            .canvas-container {
                flex-direction: column;
            }
            .controls {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇲🇼 Malawi Districts Graph Visualization</h1>
        
        <div class="algorithm-info">
            <h4>Force-Directed Layout Algorithm (Fruchterman-Reingold)</h4>
            <p>This implementation uses the Fruchterman-Reingold algorithm to optimize node positions by simulating physical forces between nodes. Connected nodes attract each other while all nodes repel to prevent overlap.</p>
        </div>

        <div class="controls">
            <button class="btn-primary" onclick="optimizeLayout()">🔄 Optimize Layout</button>
            <button class="btn-secondary" onclick="resetLayout()">↩️ Reset to Original</button>
            <button class="btn-success" onclick="exportCoordinates()">📊 Export Coordinates</button>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value" id="nodeCount">28</div>
                <div class="metric-label">Total Districts</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="edgeCount">34</div>
                <div class="metric-label">Connections</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="iterations">0</div>
                <div class="metric-label">Iterations</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="energy">0.0</div>
                <div class="metric-label">System Energy</div>
            </div>
        </div>

        <div class="canvas-container">
            <div class="canvas-wrapper">
                <h3>Original Layout</h3>
                <canvas id="originalCanvas" width="450" height="450"></canvas>
            </div>
            <div class="canvas-wrapper">
                <h3>Optimized Layout</h3>
                <canvas id="optimizedCanvas" width="450" height="450"></canvas>
            </div>
        </div>

        <div class="info-panel">
            <h3>Optimized Coordinates Output</h3>
            <div class="coordinates-output" id="coordinatesOutput">
Click "Optimize Layout" to generate optimized coordinates...
            </div>
        </div>
    </div>

    <script>
        // Malawi Districts Data
        const malawiData = {
            nodes: [
                { "id": "Blantyre", "x": 0.9134213014976535, "y": 0.2540740323898225 },
                { "id": "Chikwawa", "x": 0.14374226893980302, "y": 0.3910154112946962 },
                { "id": "Chiradzulu", "x": 0.9351749046225152, "y": 0.5027042682331085 },
                { "id": "Chitipa", "x": 0.5033532302137712, "y": 0.6371050642113303 },
                { "id": "Dedza", "x": 0.32675593364689126, "y": 0.32741458873737384 },
                { "id": "Dowa", "x": 0.44893854232683894, "y": 0.3534310438093927 },
                { "id": "Karonga", "x": 0.7719114930591756, "y": 0.7164846847486838 },
                { "id": "Kasungu", "x": 0.9486271739760203, "y": 0.03717616769235954 },
                { "id": "Lilongwe", "x": 0.03185092819745572, "y": 0.07907784991666855 },
                { "id": "Machinga", "x": 0.4976553188158377, "y": 0.15957191749775634 },
                { "id": "Mangochi", "x": 0.2417748469656349, "y": 0.22132470346325728 },
                { "id": "Mchinji", "x": 0.8029651384628501, "y": 0.4170419722297135 },
                { "id": "Mulanje", "x": 0.6998851394303303, "y": 0.7300336822154281 },
                { "id": "Mwanza", "x": 0.3093976112949879, "y": 0.9141857772478698 },
                { "id": "Mzimba", "x": 0.16190201617155997, "y": 0.8356366262711726 },
                { "id": "Neno", "x": 0.9869012833729535, "y": 0.3511167097222222 },
                { "id": "Nkhata Bay", "x": 0.0882233026546202, "y": 0.18674223158715342 },
                { "id": "Nkhotakota", "x": 0.17467106409589772, "y": 0.0010883823237957113 },
                { "id": "Nsanje", "x": 0.8093914854184416, "y": 0.5079865816371467 },
                { "id": "Ntcheu", "x": 0.8588177668360885, "y": 0.4167540312634731 },
                { "id": "Ntchisi", "x": 0.3969781197576786, "y": 0.9982702660465445 },
                { "id": "Phalombe", "x": 0.934352810085411, "y": 0.7328019939159007 },
                { "id": "Rumphi", "x": 0.2438492080065875, "y": 0.0387865957339274 },
                { "id": "Salima", "x": 0.837201462046805, "y": 0.9965726289086905 },
                { "id": "Thyolo", "x": 0.6272655175304893, "y": 0.7688215502317457 },
                { "id": "Zomba", "x": 0.7252659639019722, "y": 0.810888016094619 },
                { "id": "Balaka", "x": 0.15932838570160823, "y": 0.5698123530031478 },
                { "id": "Likoma", "x": 0.3488343806746971, "y": 0.6253864059894712 }
            ],
            edges: [
                ["Blantyre", "Chikwawa"], ["Blantyre", "Chiradzulu"], ["Blantyre", "Thyolo"],
                ["Chikwawa", "Nsanje"], ["Chikwawa", "Mwanza"], ["Chiradzulu", "Zomba"],
                ["Chiradzulu", "Phalombe"], ["Chitipa", "Karonga"], ["Dedza", "Lilongwe"],
                ["Dedza", "Ntcheu"], ["Dowa", "Lilongwe"], ["Dowa", "Ntchisi"],
                ["Karonga", "Rumphi"], ["Kasungu", "Lilongwe"], ["Kasungu", "Mzimba"],
                ["Lilongwe", "Mchinji"], ["Lilongwe", "Salima"], ["Machinga", "Zomba"],
                ["Machinga", "Balaka"], ["Mangochi", "Balaka"], ["Mangochi", "Salima"],
                ["Mulanje", "Phalombe"], ["Mulanje", "Thyolo"], ["Mwanza", "Neno"],
                ["Mzimba", "Nkhata Bay"], ["Mzimba", "Rumphi"], ["Nkhata Bay", "Nkhotakota"],
                ["Nkhotakota", "Salima"], ["Nsanje", "Chikwawa"], ["Ntcheu", "Balaka"],
                ["Ntchisi", "Nkhotakota"], ["Phalombe", "Mulanje"], ["Salima", "Nkhotakota"],
                ["Zomba", "Machinga"]
            ]
        };

        // Global variables
        let currentNodes = JSON.parse(JSON.stringify(malawiData.nodes));
        let originalNodes = JSON.parse(JSON.stringify(malawiData.nodes));
        let adjacencyList = new Map();
        let isOptimizing = false;

        // Initialize adjacency list
        function initializeAdjacencyList() {
            // Initialize empty adjacency list
            malawiData.nodes.forEach(node => {
                adjacencyList.set(node.id, []);
            });
            
            // Populate adjacency list
            malawiData.edges.forEach(edge => {
                const [node1, node2] = edge;
                adjacencyList.get(node1).push(node2);
                adjacencyList.get(node2).push(node1);
            });
        }

        // Fruchterman-Reingold Algorithm Implementation
        class FruchtermanReingoldLayout {
            constructor(nodes, edges, width = 1, height = 1) {
                this.nodes = nodes;
                this.edges = edges;
                this.width = width;
                this.height = height;
                this.area = width * height;
                this.k = Math.sqrt(this.area / nodes.length); // Optimal distance between nodes
                this.iterations = 0;
                this.maxIterations = 9;
                this.temperature = 0.1;
                this.coolingRate = 0.95;
                this.minTemperature = 0.01;
            }

            // Calculate repulsive force between two nodes
            calculateRepulsiveForce(node1, node2) {
                const dx = node1.x - node2.x;
                const dy = node1.y - node2.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < 0.01) return { fx: 0, fy: 0 }; // Avoid division by zero
                
                const force = (this.k * this.k) / distance;
                return {
                    fx: (dx / distance) * force,
                    fy: (dy / distance) * force
                };
            }

            // Calculate attractive force between connected nodes
            calculateAttractiveForce(node1, node2) {
                const dx = node2.x - node1.x;
                const dy = node2.y - node1.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < 0.01) return { fx: 0, fy: 0 }; // Avoid division by zero
                
                const force = (distance * distance) / this.k;
                return {
                    fx: (dx / distance) * force,
                    fy: (dy / distance) * force
                };
            }

            // Single iteration of the algorithm
            iterate() {
                const forces = new Map();
                
                // Initialize forces
                this.nodes.forEach(node => {
                    forces.set(node.id, { fx: 0, fy: 0 });
                });

                // Calculate repulsive forces
                for (let i = 0; i < this.nodes.length; i++) {
                    for (let j = i + 1; j < this.nodes.length; j++) {
                        const node1 = this.nodes[i];
                        const node2 = this.nodes[j];
                        const repulsiveForce = this.calculateRepulsiveForce(node1, node2);
                        
                        const force1 = forces.get(node1.id);
                        const force2 = forces.get(node2.id);
                        
                        force1.fx += repulsiveForce.fx;
                        force1.fy += repulsiveForce.fy;
                        force2.fx -= repulsiveForce.fx;
                        force2.fy -= repulsiveForce.fy;
                    }
                }

                // Calculate attractive forces
                this.edges.forEach(edge => {
                    const node1 = this.nodes.find(n => n.id === edge[0]);
                    const node2 = this.nodes.find(n => n.id === edge[1]);
                    
                    if (node1 && node2) {
                        const attractiveForce = this.calculateAttractiveForce(node1, node2);
                        
                        const force1 = forces.get(node1.id);
                        const force2 = forces.get(node2.id);
                        
                        force1.fx += attractiveForce.fx;
                        force1.fy += attractiveForce.fy;
                        force2.fx -= attractiveForce.fx;
                        force2.fy -= attractiveForce.fy;
                    }
                });

                // Apply forces and update positions
                this.nodes.forEach(node => {
                    const force = forces.get(node.id);
                    const displacement = Math.sqrt(force.fx * force.fx + force.fy * force.fy);
                    
                    if (displacement > 0) {
                        const limitedDisplacement = Math.min(displacement, this.temperature);
                        node.x += (force.fx / displacement) * limitedDisplacement;
                        node.y += (force.fy / displacement) * limitedDisplacement;
                    }

                    // Keep nodes within bounds
                    node.x = Math.max(0.05, Math.min(0.95, node.x));
                    node.y = Math.max(0.05, Math.min(0.95, node.y));
                });

                // Cool down
                this.temperature = Math.max(this.minTemperature, this.temperature * this.coolingRate);
                this.iterations++;
            }

            // Calculate system energy (for monitoring convergence)
            calculateEnergy() {
                let energy = 0;
                
                // Repulsive energy
                for (let i = 0; i < this.nodes.length; i++) {
                    for (let j = i + 1; j < this.nodes.length; j++) {
                        const node1 = this.nodes[i];
                        const node2 = this.nodes[j];
                        const dx = node1.x - node2.x;
                        const dy = node1.y - node2.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        if (distance > 0) {
                            energy += (this.k * this.k) / distance;
                        }
                    }
                }
                
                // Attractive energy
                this.edges.forEach(edge => {
                    const node1 = this.nodes.find(n => n.id === edge[0]);
                    const node2 = this.nodes.find(n => n.id === edge[1]);
                    if (node1 && node2) {
                        const dx = node1.x - node2.x;
                        const dy = node1.y - node2.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        energy += (distance * distance) / this.k;
                    }
                });
                
                return energy;
            }

            // Run the complete algorithm
            async run(progressCallback) {
                for (let i = 0; i < this.maxIterations; i++) {
                    this.iterate();
                    
                    if (progressCallback) {
                        const progress = (i + 1) / this.maxIterations;
                        const energy = this.calculateEnergy();
                        await progressCallback(progress, this.iterations, energy);
                    }
                    
                    // Small delay to allow UI updates
                    await new Promise(resolve => setTimeout(resolve, 50));
                }
            }
        }

        // Canvas drawing functions
        function drawGraph(canvas, nodes, edges, title) {
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;
            const padding = 40;
            
            // Clear canvas
            ctx.clearRect(0, 0, width, height);
            
            // Draw background
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, width, height);
            
            // Draw edges
            ctx.strokeStyle = '#95a5a6';
            ctx.lineWidth = 2;
            edges.forEach(edge => {
                const node1 = nodes.find(n => n.id === edge[0]);
                const node2 = nodes.find(n => n.id === edge[1]);
                
                if (node1 && node2) {
                    ctx.beginPath();
                    ctx.moveTo(
                        node1.x * (width - 2 * padding) + padding,
                        node1.y * (height - 2 * padding) + padding
                    );
                    ctx.lineTo(
                        node2.x * (width - 2 * padding) + padding,
                        node2.y * (height - 2 * padding) + padding
                    );
                    ctx.stroke();
                }
            });
            
            // Draw nodes
            nodes.forEach(node => {
                const x = node.x * (width - 2 * padding) + padding;
                const y = node.y * (height - 2 * padding) + padding;
                
                // Node circle
                ctx.beginPath();
                ctx.arc(x, y, 8, 0, 2 * Math.PI);
                ctx.fillStyle = '#3498db';
                ctx.fill();
                ctx.strokeStyle = '#2980b9';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                // Node label
                ctx.fillStyle = '#2c3e50';
                ctx.font = '10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(node.id, x, y - 12);
            });
        }

        // Initialize and draw original layout
        function initializeVisualization() {
            initializeAdjacencyList();
            drawGraph(document.getElementById('originalCanvas'), originalNodes, malawiData.edges, 'Original');
            drawGraph(document.getElementById('optimizedCanvas'), currentNodes, malawiData.edges, 'Optimized');
        }

        // Optimize layout using Fruchterman-Reingold algorithm
        async function optimizeLayout() {
            if (isOptimizing) return;
            
            isOptimizing = true;
            currentNodes = JSON.parse(JSON.stringify(originalNodes));
            
            const layout = new FruchtermanReingoldLayout(currentNodes, malawiData.edges);
            
            await layout.run(async (progress, iterations, energy) => {
                document.getElementById('progressFill').style.width = (progress * 100) + '%';
                document.getElementById('iterations').textContent = iterations;
                document.getElementById('energy').textContent = energy.toFixed(2);
                
                // Update optimized canvas
                drawGraph(document.getElementById('optimizedCanvas'), currentNodes, malawiData.edges, 'Optimized');
            });
            
            isOptimizing = false;
            exportCoordinates();
        }

        // Reset to original layout
        function resetLayout() {
            currentNodes = JSON.parse(JSON.stringify(originalNodes));
            drawGraph(document.getElementById('optimizedCanvas'), currentNodes, malawiData.edges, 'Optimized');
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('iterations').textContent = '0';
            document.getElementById('energy').textContent = '0.0';
            document.getElementById('coordinatesOutput').textContent = 'Click "Optimize Layout" to generate optimized coordinates...';
        }

        // Export coordinates to output
        function exportCoordinates() {
            let output = "Optimized Coordinates (Fruchterman-Reingold Algorithm):\n\n";
            output += "{\n  \"nodes\": [\n";
            
            currentNodes.forEach((node, index) => {
                output += `    { "id": "${node.id}", "x": ${node.x.toFixed(6)}, "y": ${node.y.toFixed(6)} }`;
                if (index < currentNodes.length - 1) output += ",";
                output += "\n";
            });
            
            output += "  ]\n}";
            
            document.getElementById('coordinatesOutput').textContent = output;
        }

        // Initialize when page loads
        window.onload = function() {
            initializeVisualization();
        };
    </script>
</body>
</html>